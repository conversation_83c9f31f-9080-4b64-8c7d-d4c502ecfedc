import { unifiedAIService } from './unifiedAIService';

export interface ParsedResumeData {
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    location: string;
    website: string;
    linkedin: string;
  };
  summary: string;
  experience: Array<{
    company: string;
    position: string;
    startDate: string;
    endDate: string;
    current: boolean;
    description: string;
    achievements: string[];
  }>;
  education: Array<{
    institution: string;
    degree: string;
    field: string;
    startDate: string;
    endDate: string;
    gpa: string;
    achievements: string[];
  }>;
  skills: Array<{
    category: string;
    skills: Array<{
      name: string;
      proficiency: string;
    }>;
  }>;
  projects: Array<{
    name: string;
    description: string;
    technologies: string[];
    url: string;
    highlights: string[];
  }>;
}

export class ResumeParsingService {
  /**
   * Parse LinkedIn profile text into structured resume data
   */
  static async parseLinkedInProfile(linkedInText: string, userId?: string): Promise<ParsedResumeData> {
    const prompt = `
Extract structured data from the LinkedIn profile text below.

CRITICAL INSTRUCTIONS:
- Return ONLY a valid JSON object
- Do NOT include markdown code blocks (no \`\`\`json)
- Do NOT include any explanatory text
- Start your response with { and end with }

LinkedIn Profile Text:
${linkedInText}

Required JSON structure:
{
  "personalInfo": {
    "firstName": "string",
    "lastName": "string", 
    "email": "string",
    "phone": "string",
    "location": "string",
    "website": "string",
    "linkedin": "string"
  },
  "summary": "Professional summary or about section",
  "experience": [
    {
      "company": "Company Name",
      "position": "Job Title",
      "startDate": "YYYY-MM",
      "endDate": "YYYY-MM or Present",
      "current": false,
      "description": "Job description",
      "achievements": ["achievement 1", "achievement 2"]
    }
  ],
  "education": [
    {
      "institution": "School Name",
      "degree": "Degree Type",
      "field": "Field of Study",
      "startDate": "YYYY",
      "endDate": "YYYY",
      "gpa": "",
      "achievements": ["honor 1", "honor 2"]
    }
  ],
  "skills": [
    {
      "category": "Technical Skills",
      "skills": [
        {"name": "JavaScript", "proficiency": "Advanced"},
        {"name": "React", "proficiency": "Intermediate"}
      ]
    }
  ],
  "projects": [
    {
      "name": "Project Name",
      "description": "Project description",
      "technologies": ["tech1", "tech2"],
      "url": "",
      "highlights": ["highlight 1", "highlight 2"]
    }
  ]
}

Extract as much information as possible from the LinkedIn profile. Pay special attention to:
- About section for summary
- Experience section for work history
- Education section for academic background
- Skills section for technical and soft skills
- Any projects or accomplishments mentioned

If certain fields are not available, use empty strings or arrays. 
For dates, use YYYY-MM format when possible. 
For skills, categorize them logically (Technical, Soft Skills, Languages, etc.).
Set current: true for current positions (if end date is "Present" or similar).
`;

    const result = await unifiedAIService.generateContent(
      prompt,
      'resume_analysis',
      userId ? `linkedin_parse_${userId}_${this.hashContent(linkedInText)}` : undefined
    );

    if (!result.success) {
      throw new Error(result.error || 'Failed to parse LinkedIn profile');
    }

    try {
      // Handle both direct JSON and wrapped content
      let parsedData;
      if (typeof result.data === 'string') {
        // Clean and parse JSON string
        const cleanedJson = this.cleanJsonString(result.data);
        parsedData = JSON.parse(cleanedJson);
      } else if (result.data && typeof result.data === 'object') {
        // If it's already an object, check if it's wrapped in content
        if (result.data.content && typeof result.data.content === 'string') {
          // Try to parse the content as JSON
          try {
            const cleanedJson = this.cleanJsonString(result.data.content);
            parsedData = JSON.parse(cleanedJson);
          } catch {
            // If content is not JSON, create a basic structure
            parsedData = this.extractDataFromText(result.data.content);
          }
        } else {
          // Use the data directly
          parsedData = result.data;
        }
      } else {
        throw new Error('Invalid AI response format');
      }

      return this.validateAndCleanData(parsedData);
    } catch (error) {
      console.error('LinkedIn parsing error:', error);
      throw new Error('Failed to parse LinkedIn profile data');
    }
  }

  /**
   * Parse uploaded resume file into structured data
   */
  static async parseUploadedResume(resumeText: string, userId?: string): Promise<ParsedResumeData> {
    const prompt = `
Extract structured data from the resume text below.

CRITICAL INSTRUCTIONS:
- Return ONLY a valid JSON object
- Do NOT include markdown code blocks (no \`\`\`json)
- Do NOT include any explanatory text
- Start your response with { and end with }

Resume Text:
${resumeText}

Required JSON structure:
{
  "personalInfo": {
    "firstName": "string",
    "lastName": "string", 
    "email": "string",
    "phone": "string",
    "location": "string",
    "website": "string",
    "linkedin": "string"
  },
  "summary": "Professional summary or objective section",
  "experience": [
    {
      "company": "Company Name",
      "position": "Job Title",
      "startDate": "YYYY-MM",
      "endDate": "YYYY-MM or Present",
      "current": false,
      "description": "Job description",
      "achievements": ["achievement 1", "achievement 2"]
    }
  ],
  "education": [
    {
      "institution": "School Name",
      "degree": "Degree Type",
      "field": "Field of Study",
      "startDate": "YYYY",
      "endDate": "YYYY",
      "gpa": "3.8",
      "achievements": ["honor 1", "honor 2"]
    }
  ],
  "skills": [
    {
      "category": "Technical Skills",
      "skills": [
        {"name": "JavaScript", "proficiency": "Advanced"},
        {"name": "React", "proficiency": "Intermediate"}
      ]
    }
  ],
  "projects": [
    {
      "name": "Project Name",
      "description": "Project description",
      "technologies": ["tech1", "tech2"],
      "url": "",
      "highlights": ["highlight 1", "highlight 2"]
    }
  ]
}

Extract all information from the resume. Look for:
- Contact information at the top
- Professional summary or objective
- Work experience with dates, companies, and achievements
- Education with degrees, institutions, and dates
- Skills section (technical, soft skills, languages)
- Projects, certifications, or additional sections

If certain fields are not available, use empty strings or arrays.
For dates, use YYYY-MM format when possible.
For skills, categorize them logically and estimate proficiency levels.
Set current: true for current positions.
`;

    const result = await unifiedAIService.generateContent(
      prompt,
      'resume_analysis',
      userId ? `resume_parse_${userId}_${this.hashContent(resumeText)}` : undefined
    );

    if (!result.success) {
      throw new Error(result.error || 'Failed to parse resume');
    }

    try {
      // Handle both direct JSON and wrapped content
      let parsedData;
      if (typeof result.data === 'string') {
        // Clean and parse JSON string
        const cleanedJson = this.cleanJsonString(result.data);
        parsedData = JSON.parse(cleanedJson);
      } else if (result.data && typeof result.data === 'object') {
        // If it's already an object, check if it's wrapped in content
        if (result.data.content && typeof result.data.content === 'string') {
          // Try to parse the content as JSON
          try {
            const cleanedJson = this.cleanJsonString(result.data.content);
            parsedData = JSON.parse(cleanedJson);
          } catch {
            // If content is not JSON, create a basic structure
            parsedData = this.extractDataFromText(result.data.content);
          }
        } else {
          // Use the data directly
          parsedData = result.data;
        }
      } else {
        throw new Error('Invalid AI response format');
      }

      return this.validateAndCleanData(parsedData);
    } catch (error) {
      console.error('Resume parsing error:', error);
      throw new Error('Failed to parse uploaded resume data');
    }
  }

  /**
   * Clean JSON string by removing markdown formatting and other artifacts
   */
  private static cleanJsonString(jsonString: string): string {
    // Remove markdown code blocks
    let cleaned = jsonString.replace(/```json\s*/g, '').replace(/```\s*/g, '');

    // Remove any leading/trailing whitespace
    cleaned = cleaned.trim();

    // If the string doesn't start with { or [, try to find the JSON part
    if (!cleaned.startsWith('{') && !cleaned.startsWith('[')) {
      const jsonMatch = cleaned.match(/\{[\s\S]*\}|\[[\s\S]*\]/);
      if (jsonMatch) {
        cleaned = jsonMatch[0];
      }
    }

    return cleaned;
  }

  /**
   * Extract basic data from plain text when JSON parsing fails
   */
  private static extractDataFromText(text: string): ParsedResumeData {
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);

    // Basic extraction logic
    const personalInfo = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      location: '',
      website: '',
      linkedin: ''
    };

    // Try to extract email
    const emailMatch = text.match(/[\w\.-]+@[\w\.-]+\.\w+/);
    if (emailMatch) {
      personalInfo.email = emailMatch[0];
    }

    // Try to extract phone
    const phoneMatch = text.match(/[\+]?[1-9]?[\d\s\-\(\)]{10,}/);
    if (phoneMatch) {
      personalInfo.phone = phoneMatch[0];
    }

    // Try to extract name from first few lines
    if (lines.length > 0) {
      const nameParts = lines[0].split(' ');
      if (nameParts.length >= 2) {
        personalInfo.firstName = nameParts[0];
        personalInfo.lastName = nameParts.slice(1).join(' ');
      }
    }

    return {
      personalInfo,
      summary: lines.slice(0, 3).join(' '),
      experience: [],
      education: [],
      skills: [],
      projects: []
    };
  }

  /**
   * Validate and clean parsed data
   */
  private static validateAndCleanData(data: any): ParsedResumeData {
    const defaultData: ParsedResumeData = {
      personalInfo: {
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        location: '',
        website: '',
        linkedin: ''
      },
      summary: '',
      experience: [],
      education: [],
      skills: [],
      projects: []
    };

    // Merge with defaults to ensure all fields exist
    const cleanData = {
      ...defaultData,
      ...data,
      personalInfo: {
        ...defaultData.personalInfo,
        ...data.personalInfo
      }
    };

    // Clean and validate arrays
    cleanData.experience = Array.isArray(data.experience) ? data.experience : [];
    cleanData.education = Array.isArray(data.education) ? data.education : [];
    cleanData.skills = Array.isArray(data.skills) ? data.skills : [];
    cleanData.projects = Array.isArray(data.projects) ? data.projects : [];

    return cleanData;
  }

  /**
   * Generate hash for content
   */
  private static hashContent(content: string): string {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }
}
